import { TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { DynamicFormComponent } from './dynamic-form.component';

describe('DynamicFormComponent - Positioned Groups', () => {
  let component: DynamicFormComponent;
  let fixture: any;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DynamicFormComponent],
      imports: [],
      providers: [FormBuilder]
    }).compileComponents();

    fixture = TestBed.createComponent(DynamicFormComponent);
    component = fixture.componentInstance;
  });

  describe('Group Container Processing', () => {
    it('should create group containers based on field positions', () => {
      const fieldsWithPositionedGroups = [
        { fieldName: 'regularField1', row: 1, column: 1, rowSize: 1, colSize: 1 },
        { fieldName: 'groupField1', Group: 'testGroup', row: 1, column: 2, rowSize: 1, colSize: 1 },
        { fieldName: 'groupField2', Group: 'testGroup', row: 2, column: 2, rowSize: 1, colSize: 1 },
        { fieldName: 'regularField2', row: 3, column: 1, rowSize: 1, colSize: 1 }
      ];

      const result = (component as any).processGroupContainers(fieldsWithPositionedGroups, 3);
      
      // Should have 1 group container + 2 regular fields
      expect(result.length).toBe(3);
      
      // Find the group container
      const groupContainer = result.find((item: any) => item.isGroupContainer);
      expect(groupContainer).toBeDefined();
      expect(groupContainer.groupName).toBe('testGroup');
      expect(groupContainer.groupFields.length).toBe(2);
      
      // Group should span from row 1 to row 2, column 2
      expect(groupContainer.row).toBe(1);
      expect(groupContainer.column).toBe(2);
      expect(groupContainer.rowSize).toBe(2);
      expect(groupContainer.colSize).toBe(1);
    });

    it('should calculate correct group bounds for spanning fields', () => {
      const groupFields = [
        { fieldName: 'field1', row: 1, column: 1, rowSize: 1, colSize: 2 }, // Spans 2 columns
        { fieldName: 'field2', row: 2, column: 2, rowSize: 1, colSize: 1 },
        { fieldName: 'field3', row: 3, column: 1, rowSize: 1, colSize: 1 }
      ];

      const bounds = (component as any).calculateGroupBounds(groupFields);
      
      expect(bounds.minRow).toBe(1);
      expect(bounds.maxRow).toBe(3);
      expect(bounds.minColumn).toBe(1);
      expect(bounds.maxColumn).toBe(2); // field1 spans to column 2
      expect(bounds.rowSpan).toBe(3);
      expect(bounds.colSpan).toBe(2);
    });

    it('should handle multiple groups with different positions', () => {
      const fieldsWithMultipleGroups = [
        { fieldName: 'group1Field1', Group: 'group1', row: 1, column: 1, rowSize: 1, colSize: 1 },
        { fieldName: 'group1Field2', Group: 'group1', row: 1, column: 2, rowSize: 1, colSize: 1 },
        { fieldName: 'group2Field1', Group: 'group2', row: 2, column: 1, rowSize: 1, colSize: 2 },
        { fieldName: 'regularField', row: 3, column: 1, rowSize: 1, colSize: 1 }
      ];

      const result = (component as any).processGroupContainers(fieldsWithMultipleGroups, 3);
      
      // Should have 2 group containers + 1 regular field
      expect(result.length).toBe(3);
      
      const group1Container = result.find((item: any) => item.groupName === 'group1');
      const group2Container = result.find((item: any) => item.groupName === 'group2');
      
      expect(group1Container).toBeDefined();
      expect(group2Container).toBeDefined();
      
      // Group1 should span row 1, columns 1-2
      expect(group1Container.row).toBe(1);
      expect(group1Container.column).toBe(1);
      expect(group1Container.colSize).toBe(2);
      
      // Group2 should be at row 2, spanning 2 columns
      expect(group2Container.row).toBe(2);
      expect(group2Container.column).toBe(1);
      expect(group2Container.colSize).toBe(2);
    });

    it('should sort results by grid position', () => {
      const fieldsOutOfOrder = [
        { fieldName: 'field3', row: 3, column: 1, rowSize: 1, colSize: 1 },
        { fieldName: 'groupField', Group: 'testGroup', row: 1, column: 2, rowSize: 1, colSize: 1 },
        { fieldName: 'field1', row: 1, column: 1, rowSize: 1, colSize: 1 },
        { fieldName: 'field2', row: 2, column: 1, rowSize: 1, colSize: 1 }
      ];

      const result = (component as any).processGroupContainers(fieldsOutOfOrder, 3);
      
      // Should be sorted by row, then column
      expect(result[0].row).toBe(1);
      expect(result[0].column).toBe(1);
      expect(result[1].row).toBe(1);
      expect(result[1].column).toBe(2);
      expect(result[2].row).toBe(2);
    });
  });

  describe('Integration with Grid Distribution', () => {
    it('should integrate group containers with grid distribution', () => {
      const testFields = [
        { fieldName: 'regular1', row: 1, column: 1, rowSize: 1, colSize: 1 },
        { fieldName: 'grouped1', Group: 'testGroup', row: 1, column: 2, rowSize: 1, colSize: 1 },
        { fieldName: 'grouped2', Group: 'testGroup', row: 2, column: 2, rowSize: 1, colSize: 1 },
        { fieldName: 'regular2', row: 3, column: 1, rowSize: 1, colSize: 1 }
      ];

      const result = (component as any).distributeFields(testFields, 3);
      
      expect(result.useGrid).toBe(true);
      expect(result.fields.length).toBe(3); // 2 regular + 1 group container
      
      const groupContainer = result.fields.find((f: any) => f.isGroupContainer);
      expect(groupContainer).toBeDefined();
      expect(groupContainer.groupFields.length).toBe(2);
    });
  });
});
